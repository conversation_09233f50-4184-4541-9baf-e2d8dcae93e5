stages:
  - apidocs
  - build
  - package
  - deploy-qas
  - deploy-stg
  - deploy-prd

apidocs:
  image: $CI_REGISTRY/devops/java/apidocs
  stage: apidocs
  only:
    - master
    - /feature.*/
  script:
    - docsctl
  tags:
    - docker

build:
  image: $CI_REGISTRY/devops/java/build
  stage: build
  only:
    - master
    - /feature.*/
  script:
    - build
  artifacts:
    paths:
      - dist
    expire_in: 1 hrs
  tags:
    - docker

package:
  image: $CI_REGISTRY/devops/docker/package
  stage: package
  only:
    - master
    - /feature.*/
  variables:
    GIT_STRATEGY: none
  script:
    - cd dist
    - package
  tags:
    - docker

.deploy_job: &deploy_job
  image: $CI_REGISTRY/devops/docker/deploy
  only:
    - master
  variables:
    GIT_STRATEGY: none
  dependencies: []
  tags:
    - docker

deploy-qas:
  <<: *deploy_job
  stage: deploy-qas
  only:
    - master
    - /feature.*/
  environment:
    name: wanda-qas
  script:
    - deploy

deploy-stg:
  <<: *deploy_job
  stage: deploy-stg
  environment:
    name: wanda-stg
  script:
    - deploy
  when: manual

deploy-prd:
  <<: *deploy_job
  stage: deploy-prd
  environment:
    name: wanda-prd
  script:
    - deploy
  when: manual