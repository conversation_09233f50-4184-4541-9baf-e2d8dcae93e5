package wanda.member.mission.admin.contract;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import wanda.member.mission.admin.contract.dto.CommonResponse;
import wanda.member.mission.admin.contract.dto.ConstraintDto;

/**
 * 约束服务
 *
 * <AUTHOR>
 **/
@FeignClient(contextId = "constraintAdminService", name = ServiceAutoConfiguration.APPLICATION_NAME)
public interface ConstraintAdminService {

    /**
     * 保存任务
     *
     * @param request
     * @return
     */
    @PostMapping(value = "member/constraint/save")
    CommonResponse save(@RequestBody ConstraintDto.SaveRequest request);

    /**
     * 获取任务详情
     *
     * @param constraintId
     * @return
     */
    @RequestMapping(value = "member/constraint/get")
    ConstraintDto.ConstraintDetail get(@RequestParam Long constraintId);

    /**
     * 查询任务列表
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "member/constraint/pageList")
    ConstraintDto.PageListResponse pageList(@RequestBody ConstraintDto.PageListRequest request);

}
