package wanda.member.mission.admin.contract.dto;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import wanda.member.mission.admin.contract.enums.ErrorCodeEnum;

/**
 * <AUTHOR>
 **/
@Getter
@Setter
@ToString
public class CommonResponse {
    private int code;
    private String errorMsg;

    public CommonResponse() {

    }

    public CommonResponse(ErrorCodeEnum errorCodeEnum) {
        this.code = errorCodeEnum.value();
        this.errorMsg = errorCodeEnum.displayName();
    }


}
