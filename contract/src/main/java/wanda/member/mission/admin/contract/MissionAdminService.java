package wanda.member.mission.admin.contract;

import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import wanda.member.mission.admin.contract.dto.CommonResponse;
import wanda.member.mission.admin.contract.dto.MissionDto;

/**
 * 任务服务
 *
 * <AUTHOR>
 **/
@FeignClient(contextId = "missionAdminService", name = ServiceAutoConfiguration.APPLICATION_NAME)
public interface MissionAdminService {

    /**
     * 保存任务
     *
     * @param request
     * @return
     */
    @PostMapping(value = "member/mission/save")
    CommonResponse save(@RequestBody MissionDto.SaveRequest request);

    /**
     * 更新任务状态
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "member/mission/updateStatus")
    CommonResponse updateStatus(@RequestBody MissionDto.UpdateStatusRequest request);

    /**
     * 获取任务详情
     *
     * @param missionId
     * @return
     */
    @RequestMapping(value = "member/mission/get")
    MissionDto.MissionDetail get(@RequestParam Long missionId);

    /**
     * 查询任务列表
     *
     * @param request
     * @return
     */
    @RequestMapping(value = "member/mission/pageList")
    MissionDto.PageListResponse pageList(@RequestBody MissionDto.PageListRequest request);

}
