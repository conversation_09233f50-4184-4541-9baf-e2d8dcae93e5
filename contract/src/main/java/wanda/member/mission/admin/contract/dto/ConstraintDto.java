package wanda.member.mission.admin.contract.dto;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 **/
public class ConstraintDto {
    @Data
    public static class SaveRequest {
        private Long id;
        /**
         * 约束名称
         */
        private String name;
        /**
         * 约束描述
         */
        private String desc;
        /**
         * 约束周期
         */
        private Integer period;
        /**
         * 周期值
         */
        private Integer periodValue;
        /**
         * 周期单位
         */
        private Integer periodUnit;
        /**
         * 可完成次数
         */
        private Integer maxTimes;
        /**
         * 场景id
         */
        private Integer sceneId;

        /**
         * 页面id
         */
        private List<Integer> pageIdList;
        /**
         * 页面url
         */
        private List<String> pageUrlList;
        /**
         * 单页面最大完成次数
         */
        private Integer pageMaxTimes;

        /**
         * 正向反向
         */
        private Integer direction;

        /**
         * 关联影片id
         */
        private List<Integer> filmIdList;
        /**
         * 关联影片id
         */
        private List<Integer> goodsIdList;
        /**
         * 校验是否购票
         */
        private Boolean isCheckBuyTicket;
        /**
         * 校验是否购票的时间范围x天
         */
        private Integer buyTicketDays;
        /**
         * 操作人id
         */
        private Integer operatorId;
        /**
         * 操作人名称
         */
        private String operatorName;
    }


    @Data
    public static class ConstraintDetail {
        private Long id;
        /**
         * 约束名称
         */
        private String name;
        /**
         * 约束描述
         */
        private String desc;
        /**
         * 约束周期
         */
        private Integer period;
        /**
         * 周期值
         */
        private Integer periodValue;
        /**
         * 周期单位
         */
        private Integer periodUnit;
        /**
         * 可完成次数
         */
        private Integer maxTimes;
        /**
         * 场景id
         */
        private Integer sceneId;

        /**
         * 页面id
         */
        private List<Integer> pageIdList;
        /**
         * 页面url
         */
        private List<String> pageUrlList;
        /**
         * 单页面最大完成次数
         */
        private Integer pageMaxTimes;

        /**
         * 正向反向
         */
        private Integer direction;

        /**
         * 关联影片id
         */
        private List<Integer> filmIdList;
        /**
         * 关联影片id
         */
        private List<Integer> goodsIdList;
        /**
         * 校验是否购票
         */
        private Boolean isCheckBuyTicket;
        /**
         * 校验是否购票的时间范围x天
         */
        private Integer buyTicketDays;

        private Integer createUserId;
        private String createUser;
        private LocalDateTime createTime;
        private Integer updateUserId;
        private String updateUser;
        private LocalDateTime updateTime;
    }

    @Data
    public static class PageListRequest {
        private Long constraintId;
        private String name;
        private Integer sceneId;

        private int pageIndex;
        private int pageSize;
    }

    @Data
    public static class PageListResponse {
        private int count;
        private List<ConstraintDetail> constraintDetailList;
    }


}
