package wanda.member.mission.admin.contract.enums;

import wanda.stark.core.lang.EnumDisplayNameSupport;
import wanda.stark.core.lang.EnumValueSupport;
import wanda.stark.core.lang.Enums;

/**
 * 错误码
 *
 * <AUTHOR>
 **/
public enum ErrorCodeEnum implements EnumValueSupport, EnumDisplayNameSupport {

    SUCCESS(0, "成功"),
    PRIZE_RELATED(10001, "奖池已关联其他活动"),
    PRIZE_RELATE_FAILED(10001, "奖池关联活动失败"),
    ;

    ErrorCodeEnum(int value, String displayName) {
        this.value = value;
        this.displayName = displayName;
    }

    private final int value;
    private final String displayName;

    @Override
    public int value() {
        return value;
    }

    @Override
    public String displayName() {
        return displayName;
    }

    public static ErrorCodeEnum valueOf(int value) {
        return Enums.valueOf(ErrorCodeEnum.class, value);
    }


}
