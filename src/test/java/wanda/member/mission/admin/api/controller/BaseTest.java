package wanda.member.mission.admin.api.controller;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.web.client.TestRestTemplate;
import org.springframework.http.*;
import org.springframework.util.StringUtils;
import wanda.member.mission.admin.api.ApiApplication;
import wanda.stark.core.codec.JsonUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 **/
@SpringBootTest(classes = {ApiApplication.class}, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
public class BaseTest {
    public static final String QAS_DOMAIN = "http://cloud-qas.wandafilm.com/api/";
    private static final String TOKEN = "WVZEbTVUM0FlY3EvOHlHN0NQaHIyeU83TW1PL1RLd2piQnAzZTVMTk1rWVllOFNVV01ydFI1WkplWTFnNWZyRWRhZHdpSUNsNHk1akMvWDFCL2VrcXdua2NlUlpQcEtmbmswRmx3cXpnMUMzUVJmWEpxbkVCNTg3STB6VmpRaUxZSEVBNjRjRTdjc2lxS0pPVGZhdzNIc1pVdStYVzhMZm5tWkJ6VEppaUdIblZQeUdVaDR0RjBTSW0vK2hCL0YyR3VlTHU5RGtFNGtXUE9ndGs0MEtQL20rQ3JSVlErVTY5Nzk3aDl5bWZpcGEwYTJOUVlFOUNTU0xTOVBrTHMybzhxR3pYcWl1bm5PSTZIc3UxMzdKYjhQMzZWZEZEcEluakFhanBibFozempkWEJDSDVrdFdXTDJIWkhhWmZSM2JZTVQ0cC9GWXlzRnZYWjZiMm8zVGFMZ2NBdHNkVzBlSmdmZlp6cm9pZVg2ZXNLMndGa25ZcHk1akROMTJEK2RhRFRPeG92V0FCc3h1UDBHSFYrdHFWUUxPVXlWZ1N1YUh1dWFiUzNSNTllSi9sWTJBWUhXd3E4dWxtWENRUDhzSw==";
    private static final String loginUrl = "portal/auth/login";

    @Getter
    @Autowired
    private TestRestTemplate restTemplate;

    public String login() {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        HttpEntity entity = new HttpEntity<>(headers);

        Map<String, Object> params = new HashMap<>();
        params.put("loginName", "admin1");
        params.put("password", "123456");

        ResponseEntity<String> response = restTemplate.exchange(QAS_DOMAIN + login(), HttpMethod.POST, entity, String.class, JsonUtils.encode(params));
        System.out.println("response == " + response);
        return "";
    }

    public HttpEntity buildHttpEntity(String body) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.set("Authorization", TOKEN);
        if (StringUtils.isEmpty(body)) {
            return new HttpEntity<>(headers);
        } else {
            return new HttpEntity<>(body, headers);
        }
    }


}
