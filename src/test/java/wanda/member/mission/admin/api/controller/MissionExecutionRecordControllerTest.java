package wanda.member.mission.admin.api.controller;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import wanda.member.mission.admin.api.vo.MissionExecutionRecordVo;
import wanda.stark.core.codec.JsonUtils;

import java.time.LocalDateTime;

/**
 * 任务执行记录控制器测试
 * <AUTHOR>
 **/
public class MissionExecutionRecordControllerTest extends BaseTest {

    @Test
    public void queryTest() {
        String url = "/member/mission/execution-record/query";
        MissionExecutionRecordVo.QueryRequestVo requestVo = new MissionExecutionRecordVo.QueryRequestVo();
        
        // 设置查询条件
        requestVo.setMissionName("影评");  // 模糊搜索任务名称
        requestVo.setMissionId(123L);     // 精准搜索任务ID
        requestVo.setStartTime(LocalDateTime.of(2025, 1, 1, 0, 0, 0));
        requestVo.setEndTime(LocalDateTime.of(2025, 1, 31, 23, 59, 59));
        requestVo.setExecutionStatus(1);  // 1-成功，2-失败，null-全部
        requestVo.setMemberNo("E51000000000000000");  // 精准搜索会员编号
        
        // 分页参数
        requestVo.setPageIndex(1);
        requestVo.setPageSize(10);

        HttpEntity httpEntity = buildHttpEntity(JsonUtils.encode(requestVo));
        ResponseEntity response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, String.class);
        System.out.println("查询任务执行记录响应 == " + response.getBody());
    }

    @Test
    public void queryAllStatusTest() {
        String url = "/member/mission/execution-record/query";
        MissionExecutionRecordVo.QueryRequestVo requestVo = new MissionExecutionRecordVo.QueryRequestVo();
        
        // 查询全部状态
        requestVo.setExecutionStatus(null);  // 全部状态
        requestVo.setPageIndex(1);
        requestVo.setPageSize(20);

        HttpEntity httpEntity = buildHttpEntity(JsonUtils.encode(requestVo));
        ResponseEntity response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, String.class);
        System.out.println("查询全部状态任务执行记录响应 == " + response.getBody());
    }

    @Test
    public void queryFailedRecordsTest() {
        String url = "/member/mission/execution-record/query";
        MissionExecutionRecordVo.QueryRequestVo requestVo = new MissionExecutionRecordVo.QueryRequestVo();
        
        // 只查询失败记录
        requestVo.setExecutionStatus(2);  // 2-失败
        requestVo.setPageIndex(1);
        requestVo.setPageSize(10);

        HttpEntity httpEntity = buildHttpEntity(JsonUtils.encode(requestVo));
        ResponseEntity response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, String.class);
        System.out.println("查询失败任务执行记录响应 == " + response.getBody());
    }

    @Test
    public void compensationTest() {
        String url = "/member/mission/execution-record/compensation";
        MissionExecutionRecordVo.CompensationRequestVo requestVo = new MissionExecutionRecordVo.CompensationRequestVo();
        
        // 设置补偿调用参数
        requestVo.setRecordCode("REC20250115143000001");  // 任务执行记录编码
        requestVo.setOperationRemark("手动补偿调用，原因：接口超时");

        HttpEntity httpEntity = buildHttpEntity(JsonUtils.encode(requestVo));
        ResponseEntity response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, String.class);
        System.out.println("补偿调用响应 == " + response.getBody());
    }

    @Test
    public void queryByTimeRangeTest() {
        String url = "/member/mission/execution-record/query";
        MissionExecutionRecordVo.QueryRequestVo requestVo = new MissionExecutionRecordVo.QueryRequestVo();
        
        // 按时间段查询
        requestVo.setStartTime(LocalDateTime.of(2025, 1, 15, 0, 0, 0));
        requestVo.setEndTime(LocalDateTime.of(2025, 1, 15, 23, 59, 59));
        requestVo.setPageIndex(1);
        requestVo.setPageSize(50);

        HttpEntity httpEntity = buildHttpEntity(JsonUtils.encode(requestVo));
        ResponseEntity response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, String.class);
        System.out.println("按时间段查询任务执行记录响应 == " + response.getBody());
    }

    @Test
    public void queryByMemberNoTest() {
        String url = "/member/mission/execution-record/query";
        MissionExecutionRecordVo.QueryRequestVo requestVo = new MissionExecutionRecordVo.QueryRequestVo();
        
        // 按会员编号查询
        requestVo.setMemberNo("E51000000000000000");
        requestVo.setPageIndex(1);
        requestVo.setPageSize(10);

        HttpEntity httpEntity = buildHttpEntity(JsonUtils.encode(requestVo));
        ResponseEntity response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, String.class);
        System.out.println("按会员编号查询任务执行记录响应 == " + response.getBody());
    }
}
