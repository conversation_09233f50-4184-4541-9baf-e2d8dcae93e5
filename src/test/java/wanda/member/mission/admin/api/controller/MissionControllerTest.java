package wanda.member.mission.admin.api.controller;

import org.junit.jupiter.api.Test;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import wanda.member.mission.admin.api.vo.MissionVo;
import wanda.member.mission.common.contract.enums.MissionStatusEnum;
import wanda.stark.core.codec.JsonUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
public class MissionControllerTest extends BaseTest {

    @Test
    public void saveTest() {
        int flag = 88;
        MissionVo.SaveVo saveVo = new MissionVo.SaveVo();
        saveVo.setId(34L);
        saveVo.setSceneId(flag);
        saveVo.setType(flag);
        saveVo.setSceneName("vo-scene-name_" + flag);
        saveVo.setConstraintId(flag);
        saveVo.setPrizeGroupId(1351);
        saveVo.setAutoReward(true);
        saveVo.setName("影评哪吒给成长值");
        saveVo.setDesc("vo_desc_" + flag);
        saveVo.setIcon("vo_icon_" + flag);
        saveVo.setLoginIcon("vo_login_icon_" + flag);
        saveVo.setPosition(flag);
        saveVo.setOnlineTime("2025-04-01 13:04:45");
        saveVo.setOfflineTime("2025-12-23 21:23:36");
        saveVo.setUndoneIcon("vo_undone_icon_" + flag);
        saveVo.setReceiveIcon("vo_receive_icon_" + flag);
        saveVo.setDoneIcon("vo-done-icon_" + flag);

        List<MissionVo.PublishPlatformVo> platformVoList = new ArrayList<>();
        for (int i = 5; i < 7; i++) {
            MissionVo.PublishPlatformVo platformVo = new MissionVo.PublishPlatformVo();
            platformVo.setPlatformType(i);
            platformVo.setLogicalOperator(i);
            platformVo.setVersion("vo_ver_" + i);
            platformVo.setLink("vo_line_" + i);
            platformVoList.add(platformVo);
        }
        saveVo.setPlatformList(platformVoList);

        String url = "/member/mission/save";
        HttpEntity httpEntity = buildHttpEntity(JsonUtils.encode(saveVo));
        ResponseEntity response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, String.class);
        System.out.println("response == " + response);
    }


    @Test
    public void onlineTest() {
        Long missionId = 32L;
        String url = "/member/mission/online?missionId=" + missionId;
        HttpEntity httpEntity = buildHttpEntity(null);
        ResponseEntity response = getRestTemplate().exchange(url, HttpMethod.GET, httpEntity, String.class);
        System.out.println("response == " + response);
    }

    @Test
    public void offlineTest() {
        Long missionId = 32L;
        String url = "/member/mission/offline?missionId=" + missionId;
        HttpEntity httpEntity = buildHttpEntity(null);
        ResponseEntity response = getRestTemplate().exchange(url, HttpMethod.GET, httpEntity, String.class);
        System.out.println("response == " + JsonUtils.encode(response.getBody()));
    }

    @Test
    public void getTest() {
        Long missionId = 32L;
        String url = "/member/mission/get?missionId=" + missionId;
        HttpEntity httpEntity = buildHttpEntity(null);
        ResponseEntity response = getRestTemplate().exchange(url, HttpMethod.GET, httpEntity, String.class);
        System.out.println("response == " + response.getBody());
    }

    @Test
    public void pageListTest() {
        String url = "/member/mission/pageList";
        MissionVo.PageListRequestVo requestVo = new MissionVo.PageListRequestVo();
        requestVo.setMissionId(4L);
        requestVo.setName("2");
        requestVo.setSceneId(14);
        requestVo.setStatus(MissionStatusEnum.ON_LINE.value());
        requestVo.setConstraintId(13);
        requestVo.setPrizeGroupId(15);

        requestVo.setStartTime(LocalDateTime.of(2025, 5, 1, 0, 0, 0));
        requestVo.setEndTime(LocalDateTime.of(2025, 5, 25, 0, 0, 0));

        requestVo.setPageIndex(1);
        requestVo.setPageSize(10);

        HttpEntity httpEntity = buildHttpEntity(JsonUtils.encode(requestVo));
        ResponseEntity response = getRestTemplate().exchange(url, HttpMethod.POST, httpEntity, String.class);
        System.out.println("response == " + response.getBody());
    }


}
