package wanda.member.mission.admin.api.mapper;

import wanda.member.mission.admin.api.vo.MissionExecutionRecordVo;
import wanda.member.mission.admin.api.vo.UserVo;
import wanda.member.mission.admin.contract.dto.MissionExecutionRecordDto;
import wanda.stark.mapper.Mapper;

/**
 * 任务执行记录映射器
 * <AUTHOR>
 **/
public class MissionExecutionRecordMapper {

    /**
     * 查询请求VO转DTO
     */
    public static MissionExecutionRecordDto.QueryRequest toDto(MissionExecutionRecordVo.QueryRequestVo requestVo) {
        Mapper<MissionExecutionRecordVo.QueryRequestVo, MissionExecutionRecordDto.QueryRequest> mapper =
                Mapper.getMapper(MissionExecutionRecordVo.QueryRequestVo.class, MissionExecutionRecordDto.QueryRequest.class);
        MissionExecutionRecordDto.QueryRequest request = new MissionExecutionRecordDto.QueryRequest();
        mapper.copy(requestVo, request);
        return request;
    }

    /**
     * 查询响应DTO转VO
     */
    public static MissionExecutionRecordVo.QueryResponseVo toVo(MissionExecutionRecordDto.QueryResponse response) {
        MissionExecutionRecordVo.QueryResponseVo responseVo = new MissionExecutionRecordVo.QueryResponseVo();
        responseVo.setCount(response.getCount());
        
        if (response.getCount() > 0 && response.getRecordList() != null) {
            responseVo.setRecordList(response.getRecordList().stream()
                    .map(MissionExecutionRecordMapper::toDetailVo)
                    .collect(java.util.stream.Collectors.toList()));
        }
        
        return responseVo;
    }

    /**
     * 执行记录详情DTO转VO
     */
    public static MissionExecutionRecordVo.ExecutionRecordDetailVo toDetailVo(MissionExecutionRecordDto.ExecutionRecordDetail detail) {
        Mapper<MissionExecutionRecordDto.ExecutionRecordDetail, MissionExecutionRecordVo.ExecutionRecordDetailVo> mapper =
                Mapper.getMapper(MissionExecutionRecordDto.ExecutionRecordDetail.class, MissionExecutionRecordVo.ExecutionRecordDetailVo.class);
        MissionExecutionRecordVo.ExecutionRecordDetailVo detailVo = new MissionExecutionRecordVo.ExecutionRecordDetailVo();
        mapper.copy(detail, detailVo);
        
        // 设置执行状态描述
        if (detail.getExecutionStatus() != null) {
            detailVo.setExecutionStatusDesc(detail.getExecutionStatus() == 1 ? "成功" : "失败");
        }
        
        // 失败原因处理：成功时显示"——"
        if (detail.getExecutionStatus() != null && detail.getExecutionStatus() == 1) {
            detailVo.setFailureReason("——");
        }
        
        return detailVo;
    }

    /**
     * 补偿调用请求VO转DTO
     */
    public static MissionExecutionRecordDto.CompensationRequest toDto(MissionExecutionRecordVo.CompensationRequestVo requestVo, UserVo userVo) {
        MissionExecutionRecordDto.CompensationRequest request = new MissionExecutionRecordDto.CompensationRequest();
        request.setRecordCode(requestVo.getRecordCode());
        request.setOperationRemark(requestVo.getOperationRemark());
        request.setOperatorId(userVo.getUserId().intValue());
        request.setOperatorName(userVo.getName());
        return request;
    }
}
