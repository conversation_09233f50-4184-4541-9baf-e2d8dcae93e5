package wanda.member.mission.admin.api.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务执行记录VO
 *
 * <AUTHOR>
 **/
public class MissionExecutionRecordVo {

    @Data
    public static class QueryRequestVo {
        /**
         * 任务名称（支持模糊搜索）
         */
        private String missionName;

        /**
         * 任务ID（支持精准搜索）
         */
        private Long missionId;

        /**
         * 任务执行开始时间
         */
        private LocalDateTime startTime;

        /**
         * 任务执行结束时间
         */
        private LocalDateTime endTime;

        /**
         * 执行状态：1-成功，2-失败，null-全部
         */
        private Integer executionStatus;

        /**
         * 会员编号（支持精准搜索）
         */
        private String memberNo;

        /**
         * 分页参数
         */
        private int pageIndex = 1;
        private int pageSize = 10;
    }

    @Data
    public static class QueryResponseVo {
        /**
         * 总记录数
         */
        private int count;

        /**
         * 执行记录列表
         */
        private List<ExecutionRecordDetailVo> recordList;
    }

    @Data
    public static class ExecutionRecordDetailVo {
        /**
         * 序号（自动生成）
         */
        private Integer sequenceNo;

        /**
         * 任务执行记录编码（唯一标识）
         */
        private String recordCode;

        /**
         * 任务ID
         */
        private Long missionId;

        /**
         * 任务名称
         */
        private String missionName;

        /**
         * 任务执行时间
         */
        private String executionTime;

        /**
         * 任务执行状态：1-成功，2-失败
         */
        private Integer executionStatus;

        /**
         * 任务执行状态描述
         */
        private String executionStatusDesc;

        /**
         * 执行失败原因
         */
        private String failureReason;

        /**
         * 会员编号
         */
        private String memberNo;

        /**
         * 系统标识
         */
        private String systemIdentifier;

        /**
         * 是否支持补偿调用
         */
        private Boolean supportCompensation;

        /**
         * 创建时间
         */
        private String createTime;
    }

    @Data
    public static class CompensationRequestVo {
        /**
         * 任务执行记录编码
         */
        private String recordCode;

        /**
         * 操作说明
         */
        private String operationRemark;
    }
}
