package wanda.member.mission.admin.api.mapper;

import wanda.member.mission.admin.api.vo.MissionVo;
import wanda.member.mission.admin.api.vo.UserVo;
import wanda.member.mission.admin.contract.dto.MissionDto;
import wanda.member.mission.common.contract.enums.MissionStatusEnum;
import wanda.stark.mapper.Mapper;

/**
 * <AUTHOR>
 **/
public class MissionMapper {

    public static MissionDto.SaveRequest toDto(MissionVo.SaveVo saveVo, UserVo userVo) {
        Mapper<MissionVo.SaveVo, MissionDto.SaveRequest> mapper =
                Mapper.getMapper(MissionVo.SaveVo.class, MissionDto.SaveRequest.class);
        MissionDto.SaveRequest saveRequest = new MissionDto.SaveRequest();
        mapper.copy(saveVo, saveRequest);
        saveRequest.setOperatorId(userVo.getUserId().intValue());
        saveRequest.setOperatorName(userVo.getName());
        return saveRequest;
    }

    public static MissionDto.UpdateStatusRequest toDto(Long missionId, MissionStatusEnum statusEnum, UserVo userVo) {
        MissionDto.UpdateStatusRequest request = new MissionDto.UpdateStatusRequest();
        request.setId(missionId);
        request.setStatus(statusEnum.value());
        request.setOperatorId(userVo.getUserId().intValue());
        request.setOperatorName(userVo.getName());
        return request;
    }

    public static MissionVo.MissionDetailVo toVo(MissionDto.MissionDetail missionDetail) {
        Mapper<MissionDto.MissionDetail, MissionVo.MissionDetailVo> mapper =
                Mapper.getMapper(MissionDto.MissionDetail.class, MissionVo.MissionDetailVo.class);
        MissionVo.MissionDetailVo missionDetailVo = new MissionVo.MissionDetailVo();
        mapper.copy(missionDetail, missionDetailVo);
        return missionDetailVo;
    }

    public static MissionDto.PageListRequest toDto(MissionVo.PageListRequestVo requestVo) {
        Mapper<MissionVo.PageListRequestVo, MissionDto.PageListRequest> mapper =
                Mapper.getMapper(MissionVo.PageListRequestVo.class, MissionDto.PageListRequest.class);
        MissionDto.PageListRequest request = new MissionDto.PageListRequest();
        mapper.copy(requestVo, request);
        return request;
    }




}
