package wanda.member.mission.admin.api.controller;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import wanda.member.mission.admin.api.facade.UserFacade;
import wanda.member.mission.admin.api.mapper.MissionExecutionRecordMapper;
import wanda.member.mission.admin.api.vo.MissionExecutionRecordVo;
import wanda.member.mission.admin.api.vo.UserVo;
import wanda.member.mission.admin.contract.MissionExecutionRecordAdminService;
import wanda.member.mission.admin.contract.dto.CommonResponse;
import wanda.member.mission.admin.contract.dto.MissionExecutionRecordDto;

/**
 * 任务执行记录查询控制器
 * <AUTHOR>
 **/
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("member/mission/execution-record")
public class MissionExecutionRecordController {
    
    private MissionExecutionRecordAdminService missionExecutionRecordAdminService;
    private UserFacade userFacade;

    /**
     * @api {POST} /member/mission/execution-record/query 查询任务执行记录
     * @apiVersion 1.0.0
     * @apiGroup 任务执行记录
     * @apiName query
     * @apiDescription 查询任务执行记录
     * @apiParam (请求体) {String} missionName 任务名称（支持模糊搜索）
     * @apiParam (请求体) {Number} missionId 任务ID（支持精准搜索）
     * @apiParam (请求体) {String} startTime 任务执行开始时间（格式：yyyy-MM-dd HH:mm:ss）
     * @apiParam (请求体) {String} endTime 任务执行结束时间（格式：yyyy-MM-dd HH:mm:ss）
     * @apiParam (请求体) {Number} executionStatus 执行状态（1-成功，2-失败，null-全部）
     * @apiParam (请求体) {String} memberNo 会员编号（支持精准搜索）
     * @apiParam (请求体) {Number} pageIndex 页码（默认1）
     * @apiParam (请求体) {Number} pageSize 每页条数（默认10）
     * @apiParamExample 请求体示例
     * {
     *   "missionName": "影评任务",
     *   "missionId": 123,
     *   "startTime": "2025-01-01 00:00:00",
     *   "endTime": "2025-01-31 23:59:59",
     *   "executionStatus": 1,
     *   "memberNo": "E51000000000000000",
     *   "pageIndex": 1,
     *   "pageSize": 10
     * }
     * @apiSuccess (响应结果) {Number} count 总记录数
     * @apiSuccess (响应结果) {Array} recordList 执行记录列表
     * @apiSuccess (响应结果) {Number} recordList.sequenceNo 序号
     * @apiSuccess (响应结果) {String} recordList.recordCode 任务执行记录编码
     * @apiSuccess (响应结果) {Number} recordList.missionId 任务ID
     * @apiSuccess (响应结果) {String} recordList.missionName 任务名称
     * @apiSuccess (响应结果) {String} recordList.executionTime 任务执行时间
     * @apiSuccess (响应结果) {Number} recordList.executionStatus 任务执行状态
     * @apiSuccess (响应结果) {String} recordList.executionStatusDesc 任务执行状态描述
     * @apiSuccess (响应结果) {String} recordList.failureReason 执行失败原因
     * @apiSuccess (响应结果) {String} recordList.memberNo 会员编号
     * @apiSuccess (响应结果) {String} recordList.systemIdentifier 系统标识
     * @apiSuccess (响应结果) {Boolean} recordList.supportCompensation 是否支持补偿调用
     * @apiSuccessExample 响应结果示例
     * {
     *   "count": 100,
     *   "recordList": [
     *     {
     *       "sequenceNo": 1,
     *       "recordCode": "001",
     *       "missionId": 123,
     *       "missionName": "影评任务",
     *       "executionTime": "2025-01-15 14:30:00",
     *       "executionStatus": 1,
     *       "executionStatusDesc": "成功",
     *       "failureReason": "——",
     *       "memberNo": "E51000000000000000",
     *       "systemIdentifier": "终端标识/编号",
     *       "supportCompensation": false
     *     }
     *   ]
     * }
     */
    @PostMapping("query")
    public MissionExecutionRecordVo.QueryResponseVo query(@RequestBody MissionExecutionRecordVo.QueryRequestVo requestVo) {
        log.info("查询任务执行记录入参:{}", requestVo);
        MissionExecutionRecordDto.QueryResponse response = missionExecutionRecordAdminService.query(
                MissionExecutionRecordMapper.toDto(requestVo));
        MissionExecutionRecordVo.QueryResponseVo responseVo = MissionExecutionRecordMapper.toVo(response);
        log.info("查询任务执行记录返回:{}", responseVo);
        return responseVo;
    }

    /**
     * @api {POST} /member/mission/execution-record/compensation 补偿调用
     * @apiVersion 1.0.0
     * @apiGroup 任务执行记录
     * @apiName compensation
     * @apiDescription 对失败的任务执行记录进行补偿调用
     * @apiParam (请求体) {String} recordCode 任务执行记录编码
     * @apiParam (请求体) {String} operationRemark 操作说明
     * @apiParamExample 请求体示例
     * {
     *   "recordCode": "REC20250115143000001",
     *   "operationRemark": "手动补偿调用"
     * }
     * @apiSuccess (响应结果) {Number} code 错误码
     * @apiSuccess (响应结果) {String} msg 错误信息
     * @apiSuccessExample 响应结果示例
     * {"code":0,"msg":"补偿调用成功"}
     */
    @PostMapping("compensation")
    public void compensation(@RequestBody MissionExecutionRecordVo.CompensationRequestVo requestVo) {
        log.info("任务执行记录补偿调用入参:{}", requestVo);
        UserVo userVo = userFacade.getUser();
        CommonResponse response = missionExecutionRecordAdminService.compensation(
                MissionExecutionRecordMapper.toDto(requestVo, userVo));
        log.info("任务执行记录补偿调用完成:{}", response);
    }
}
