package wanda.member.mission.admin.api.facade;

import org.springframework.stereotype.Component;
import wanda.member.mission.admin.api.vo.UserVo;
import wanda.stark.core.security.auth.AuthContext;

/**
 * <AUTHOR>
 **/
@Component
public class UserFacade {

    public UserVo getUser() {
        UserVo userVo = new UserVo();
//        userVo.setUserId(AuthContext.currentUser().getId());
//        userVo.setName(AuthContext.currentUser().getUsername());

        //  TODO 去掉测试代码
        userVo.setUserId(111L);
        userVo.setName("测试用户");

        return userVo;
    }

}
