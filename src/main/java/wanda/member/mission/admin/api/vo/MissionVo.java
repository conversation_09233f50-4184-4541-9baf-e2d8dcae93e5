package wanda.member.mission.admin.api.vo;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 **/
public class MissionVo {

    @Data
    public static class SaveVo {
        /**
         * 任务id
         */
        private Long id;
        /**
         * 场景id
         */
        private Integer sceneId;
        /**
         * 场景名称
         */
        private String sceneName;
        /**
         * 约束id
         */
        private Integer constraintId;
        /**
         * 奖池id
         */
        private Integer prizeGroupId;
        /**
         * 是否自动发奖
         */
        private Boolean autoReward;
        /**
         * 任务类型
         */
        private Integer type;
        /**
         * 任务名称
         */
        private String name;
        /**
         * 任务描述
         */
        private String desc;
        /**
         * 任务图片
         */
        private String icon;
        /**
         * 引导登录图片
         */
        private String loginIcon;
        /**
         * 展示顺序
         */
        private Integer position;
        /**
         * 上线时间
         */
        private String onlineTime;
        /**
         * 下线时间
         */
        private String offlineTime;
        /**
         * 未完成
         */
        private String undoneIcon;
        /**
         * 领奖图片
         */
        private String receiveIcon;
        /**
         * 完成图片
         */
        private String doneIcon;
        /**
         * 发布平台
         */
        private List<MissionVo.PublishPlatformVo> platformList;
    }

    @Data
    public static class MissionDetailVo {
        /**
         * 任务id
         */
        private Long id;
        /**
         * 场景id
         */
        private Integer sceneId;
        /**
         * 约束id
         */
        private Integer constraintId;
        /**
         * 奖池id
         */
        private Integer prizeGroupId;
        /**
         * 是否自动发奖
         */
        private Boolean autoReward;
        /**
         * 任务类型
         */
        private Integer type;
        /**
         * 任务名称
         */
        private String name;
        /**
         * 任务描述
         */
        private String desc;
        /**
         * 任务图片
         */
        private String icon;
        /**
         * 引导登录图片
         */
        private String loginIcon;
        /**
         * 展示顺序
         */
        private Integer position;
        /**
         * 上线时间
         */
        private String onlineTime;
        /**
         * 下线时间
         */
        private String offlineTime;
        /**
         * 未完成
         */
        private String undoneIcon;
        /**
         * 领奖图片
         */
        private String receiveIcon;
        /**
         * 完成图片
         */
        private String doneIcon;
        /**
         * 发布平台
         */
        private List<PublishPlatformVo> platformList;

        private String createTime;
        private String createUsername;
    }

    @Data
    public static class PublishPlatformVo {
        /**
         * 平台类型
         */
        private Integer platformType;
        /**
         * 逻辑运算符
         */
        private Integer logicalOperator;
        /**
         * 版本号
         */
        private String version;
        /**
         * 跳转链接
         */
        private String link;
    }

    @Data
    public static class PageListRequestVo {
        private Long missionId;
        private String name;
        private Integer sceneId;
        private Integer status;
        private Integer constraintId;
        private Integer prizeGroupId;
        private LocalDateTime startTime;
        private LocalDateTime endTime;

        private int pageIndex;
        private int pageSize;
    }

    @Data
    public static class PageListResponseVo {
        private int count;
        private List<MissionVo.MissionDetailVo> missionDetailList;
    }


}
