package wanda.member.mission.admin.api.controller;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import wanda.member.mission.admin.api.facade.UserFacade;
import wanda.member.mission.admin.api.mapper.MissionMapper;
import wanda.member.mission.admin.api.vo.MissionVo;
import wanda.member.mission.admin.api.vo.UserVo;
import wanda.member.mission.admin.contract.MissionAdminService;
import wanda.member.mission.admin.contract.dto.CommonResponse;
import wanda.member.mission.admin.contract.dto.MissionDto;
import wanda.member.mission.common.contract.enums.MissionStatusEnum;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Slf4j
@RestController
@AllArgsConstructor
@RequestMapping("member/mission")
public class MissionController {
    private MissionAdminService missionAdminService;
    private UserFacade userFacade;

    /**
     * @api {POST} /member/mission/save 保存任务
     * @apiVersion 1.0.0
     * @apiGroup 会员任务
     * @apiName save
     * @apiDescription 保存任务
     * @apiParam (请求体) {Number} id 任务id（编辑保存时必填）
     * @apiParam (请求体) {Number} sceneId 场景id
     * @apiParam (请求体) {String} sceneName 场景名称
     * @apiParam (请求体) {Number} constraintId 约束id
     * @apiParam (请求体) {Number} prizeGroupId 奖池id
     * @apiParam (请求体) {Bool}   autoReward 是否自动发奖
     * @apiParam (请求体) {String} type 任务类型
     * @apiParam (请求体) {String} name 任务名称
     * @apiParam (请求体) {String} desc 任务描述
     * @apiParam (请求体) {String} icon 任务图片
     * @apiParam (请求体) {String} loginIcon 引导登录图片
     * @apiParam (请求体) {Number} position 展示顺序
     * @apiParam (请求体) {String} onlineTime 上线时间
     * @apiParam (请求体) {String} offlineTime 下线时间
     * @apiParam (请求体) {String} undoneIcon 未完成
     * @apiParam (请求体) {String} receiveIcon 领奖图片
     * @apiParam (请求体) {String} doneIcon 完成图片
     * @apiParam (请求体) {Array}  platformList 发布平台
     * @apiParam (请求体) {Number} platformList.platformType 平台类型
     * @apiParam (请求体) {Number} platformList.logicalOperator 逻辑运算符
     * @apiParam (请求体) {String} platformList.version 版本号
     * @apiParam (请求体) {String} platformList.link 跳转链接
     * @apiParamExample 请求体示例
     * {}
     * @apiSuccess (响应结果) {Number} code 错误码
     * @apiSuccess (响应结果) {String} msg 错误信息
     * @apiSuccessExample 响应结果示例
     * {"code":0,"msg":""}
     */
    @PostMapping("save")
    public void save(@RequestBody MissionVo.SaveVo saveVo) {
        log.info("保存任务入参:{}", saveVo);
        UserVo userVo = userFacade.getUser();
        CommonResponse response = missionAdminService.save(MissionMapper.toDto(saveVo, userVo));
        log.info("保存任务完成:{}", response);
    }

    /**
     * @api {GET} /member/mission/online 上线任务
     * @apiVersion 1.0.0
     * @apiGroup 会员任务
     * @apiName online
     * @apiDescription 上线任务
     * @apiParam (请求体) {Number} id 任务id
     * @apiParamExample 请求体示例
     * {"missionId":111}
     * @apiSuccess (响应结果) {Number} code 错误码
     * @apiSuccess (响应结果) {String} msg 错误信息
     * @apiSuccessExample 响应结果示例
     * {"code":0,"msg":""}
     */
    @GetMapping("online")
    public void online(@RequestParam("missionId") Long missionId) {
        log.info("任务上线入参:[missionId:{}]", missionId);
        UserVo userVo = userFacade.getUser();
        CommonResponse response = missionAdminService.updateStatus(MissionMapper.toDto(missionId, MissionStatusEnum.ON_LINE, userVo));
        log.info("任务上线完成:{}", response);
    }

    /**
     * @api {GET} /member/mission/offline 下线任务
     * @apiVersion 1.0.0
     * @apiGroup 会员任务
     * @apiName offline
     * @apiDescription 下线任务
     * @apiParam (请求体) {Number} id 任务id
     * @apiParamExample 请求体示例
     * {"missionId":111}
     * @apiSuccess (响应结果) {Number} code 错误码
     * @apiSuccess (响应结果) {String} msg 错误信息
     * @apiSuccessExample 响应结果示例
     * {"code":0,"msg":""}
     */
    @GetMapping("offline")
    public void offline(@RequestParam("missionId") Long missionId) {
        log.info("任务下线入参:[missionId:{}]", missionId);
        UserVo userVo = userFacade.getUser();
        CommonResponse response = missionAdminService.updateStatus(MissionMapper.toDto(missionId, MissionStatusEnum.OFF_LINE, userVo));
        log.info("任务下线完成:{}", response);
    }

    /**
     * @api {POST} /member/mission/get 查询任务
     * @apiVersion 1.0.0
     * @apiGroup 会员任务
     * @apiName get
     * @apiDescription 查询任务
     * @apiParam (请求体) {Number} id 任务id
     * @apiParamExample 请求体示例
     * {"missionId":111}
     * @apiSuccess (响应结果) {Number} code 错误码
     * @apiSuccess (响应结果) {String} msg 错误信息
     * @apiSuccess (响应结果) {Object} data
     * @apiSuccess (响应结果) {Number} data.id 任务id（编辑保存时必填）
     * @apiSuccess (响应结果) {Number} data.sceneId 场景id
     * @apiSuccess (响应结果) {Number} data.constraintId 约束id
     * @apiSuccess (响应结果) {Number} data.prizeGroupId 奖池id
     * @apiSuccess (响应结果) {Bool}   data.autoReward 是否自动发奖
     * @apiSuccess (响应结果) {String} data.type 任务类型
     * @apiSuccess (响应结果) {String} data.name 任务名称
     * @apiSuccess (响应结果) {String} data.desc 任务描述
     * @apiSuccess (响应结果) {String} data.icon 任务图片
     * @apiSuccess (响应结果) {String} data.loginIcon 引导登录图片
     * @apiSuccess (响应结果) {Number} data.position 展示顺序
     * @apiSuccess (响应结果) {String} data.onlineTime 上线时间
     * @apiSuccess (响应结果) {String} data.offlineTime 下线时间
     * @apiSuccess (响应结果) {String} data.undoneIcon 未完成
     * @apiSuccess (响应结果) {String} data.receiveIcon 领奖图片
     * @apiSuccess (响应结果) {String} data.doneIcon 完成图片
     * @apiSuccess (响应结果) {Array}  data.platformList 发布平台
     * @apiSuccess (响应结果) {Number} data.platformList.platformType 平台类型
     * @apiSuccess (响应结果) {Number} data.platformList.logicalOperator 逻辑运算符
     * @apiSuccess (响应结果) {String} data.platformList.version 版本号
     * @apiSuccess (响应结果) {String} data.platformList.link 跳转链接
     * @apiSuccessExample 响应结果示例
     * {"code":0,"msg":""}
     */
    @GetMapping("get")
    public MissionVo.MissionDetailVo get(@RequestParam("missionId") Long missionId) {
        log.info("查询任务详情入参:[missionId:{}]", missionId);
        MissionDto.MissionDetail missionDetail = missionAdminService.get(missionId);
        MissionVo.MissionDetailVo missionDetailVo = MissionMapper.toVo(missionDetail);
        log.info("查询任务详情返回:{}", missionDetailVo);
        return missionDetailVo;
    }

    /**
     * @api {POST} /member/mission/pageList 查询任务
     * @apiVersion 1.0.0
     * @apiGroup 会员任务
     * @apiName get
     * @apiDescription 查询任务
     * @apiParam (请求体) {Number} missionId 任务id
     * @apiParamExample 请求体示例
     * {"missionId":111}
     * @apiSuccess (响应结果) {Number} code 错误码
     * @apiSuccess (响应结果) {String} msg 错误信息
     * @apiSuccess (响应结果) {Object} data
     * @apiSuccess (响应结果) {Number} data.id 任务id（编辑保存时必填）
     * @apiSuccess (响应结果) {Number} data.sceneId 场景id
     * @apiSuccess (响应结果) {Number} data.constraintId 约束id
     * @apiSuccess (响应结果) {Number} data.prizeGroupId 奖池id
     * @apiSuccess (响应结果) {Bool}   data.autoReward 是否自动发奖
     * @apiSuccess (响应结果) {String} data.type 任务类型
     * @apiSuccess (响应结果) {String} data.name 任务名称
     * @apiSuccess (响应结果) {String} data.desc 任务描述
     * @apiSuccess (响应结果) {String} data.icon 任务图片
     * @apiSuccess (响应结果) {String} data.loginIcon 引导登录图片
     * @apiSuccess (响应结果) {Number} data.position 展示顺序
     * @apiSuccess (响应结果) {String} data.onlineTime 上线时间
     * @apiSuccess (响应结果) {String} data.offlineTime 下线时间
     * @apiSuccess (响应结果) {String} data.undoneIcon 未完成
     * @apiSuccess (响应结果) {String} data.receiveIcon 领奖图片
     * @apiSuccess (响应结果) {String} data.doneIcon 完成图片
     * @apiSuccess (响应结果) {Array}  data.platformList 发布平台
     * @apiSuccess (响应结果) {Number} data.platformList.platformType 平台类型
     * @apiSuccess (响应结果) {Number} data.platformList.logicalOperator 逻辑运算符
     * @apiSuccess (响应结果) {String} data.platformList.version 版本号
     * @apiSuccess (响应结果) {String} data.platformList.link 跳转链接
     * @apiSuccessExample 响应结果示例
     * {"code":0,"msg":""}
     */
    @PostMapping("pageList")
    public MissionVo.PageListResponseVo pageList(@RequestBody MissionVo.PageListRequestVo requestVo) {
        log.info("分页查询任务入参:{}", requestVo);
        MissionDto.PageListResponse response = missionAdminService.pageList(MissionMapper.toDto(requestVo));
        MissionVo.PageListResponseVo responseVo = new MissionVo.PageListResponseVo();
        int count = response.getCount();
        responseVo.setCount(count);
        if(count > 0) {
            responseVo.setMissionDetailList(response.getMissionDetailList().stream()
                    .map(detail -> MissionMapper.toVo(detail)).collect(Collectors.toList()));
        }
        log.info("分页查询任务返回:{}", responseVo);
        return responseVo;
    }

}
