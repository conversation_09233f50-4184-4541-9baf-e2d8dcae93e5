<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>wanda.cloud</groupId>
        <artifactId>parent</artifactId>
        <version>1.1.0-SNAPSHOT</version>
        <relativePath/>
    </parent>

    <groupId>wanda.member.mission</groupId>
    <artifactId>member-mission-admin-api</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <maven.deploy.skip>true</maven.deploy.skip>
        <revision>1.0.0-SNAPSHOT</revision>
    </properties>

    <dependencies>
        <dependency>
            <groupId>wanda.cloud</groupId>
            <artifactId>wanda-api-spring-cloud-starter</artifactId>
            <version>1.1.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>wanda.member.mission</groupId>
            <artifactId>member-mission-admin-contract</artifactId>
            <version>1.0.0-SNAPSHOT</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <layers>
                        <enabled>true</enabled>
                    </layers>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
