# 任务执行记录查询接口说明

## 概述

本文档描述了任务执行记录查询接口的功能和使用方法，该接口模仿MissionController的设计模式，提供了完整的任务执行记录查询和补偿调用功能。

## 功能特性

### 1. 查询功能
- **多条件复合查询**：支持任务名称、任务ID、执行时间段、执行状态、会员编号等多个查询条件
- **模糊搜索**：任务名称支持模糊搜索
- **精准搜索**：任务ID和会员编号支持精准搜索
- **时间范围查询**：支持按执行时间段筛选，精确到年月日时分秒
- **状态筛选**：支持按执行状态筛选（成功、失败、全部）
- **分页查询**：支持分页显示和数据总数统计
- **排序**：默认按执行时间倒序排列

### 2. 补偿调用功能
- **失败任务补偿**：对执行失败的任务提供补偿调用功能
- **操作记录**：记录补偿调用的操作人和操作说明
- **状态更新**：补偿成功后自动更新执行状态和时间

## 接口详情

### 1. 查询任务执行记录

**接口地址**：`POST /member/mission/execution-record/query`

**请求参数**：
```json
{
  "missionName": "影评任务",                    // 任务名称（模糊搜索）
  "missionId": 123,                           // 任务ID（精准搜索）
  "startTime": "2025-01-01T00:00:00",         // 开始时间
  "endTime": "2025-01-31T23:59:59",           // 结束时间
  "executionStatus": 1,                       // 执行状态：1-成功，2-失败，null-全部
  "memberNo": "E51000000000000000",           // 会员编号（精准搜索）
  "pageIndex": 1,                             // 页码
  "pageSize": 10                              // 每页条数
}
```

**响应结果**：
```json
{
  "count": 100,                               // 总记录数
  "recordList": [
    {
      "sequenceNo": 1,                        // 序号
      "recordCode": "001",                    // 任务执行记录编码
      "missionId": 123,                       // 任务ID
      "missionName": "影评任务",               // 任务名称
      "executionTime": "2025-01-15 14:30:00", // 任务执行时间
      "executionStatus": 1,                   // 任务执行状态
      "executionStatusDesc": "成功",          // 任务执行状态描述
      "failureReason": "——",                  // 执行失败原因
      "memberNo": "E51000000000000000",       // 会员编号
      "systemIdentifier": "终端标识/编号",     // 系统标识
      "supportCompensation": false,           // 是否支持补偿调用
      "createTime": "2025-01-15 14:30:00"     // 创建时间
    }
  ]
}
```

### 2. 补偿调用

**接口地址**：`POST /member/mission/execution-record/compensation`

**请求参数**：
```json
{
  "recordCode": "REC20250115143000001",       // 任务执行记录编码
  "operationRemark": "手动补偿调用"           // 操作说明
}
```

**响应结果**：
```json
{
  "code": 0,
  "msg": "补偿调用成功"
}
```

## 数据字段说明

### 查询条件字段
- **missionName**：任务名称，支持模糊搜索
- **missionId**：任务ID，支持精准搜索
- **startTime/endTime**：任务执行时间段，结束时间不能早于开始时间
- **executionStatus**：执行状态，1-成功，2-失败，null或不传表示查询全部
- **memberNo**：会员编号，支持精准搜索
- **pageIndex/pageSize**：分页参数

### 返回数据字段
- **sequenceNo**：序号，系统自动生成，不可编辑
- **recordCode**：任务执行记录编码，系统自动生成的唯一标识
- **missionId/missionName**：任务ID和名称，调用时传入
- **executionTime**：任务执行时间，记录生成时的服务器时间
- **executionStatus**：执行状态，1-成功，2-失败
- **failureReason**：失败原因，成功时显示"——"
- **memberNo**：会员编号，调用时传入
- **systemIdentifier**：系统标识，调用时传入
- **supportCompensation**：是否支持补偿调用，根据失败情况确定

## 业务规则

1. **查询规则**：
   - 支持单个或多个条件组合查询
   - 时间范围查询：结束时间必须大于等于开始时间
   - 默认按执行时间倒序排列
   - 支持分页，超过单页显示条数时显示分页管理栏

2. **补偿调用规则**：
   - 仅失败状态的任务支持补偿调用
   - 补偿成功后更新执行时间、状态
   - 自动发奖任务补偿成功后给予奖励发放
   - 手动领奖任务补偿成功后更新状态为待领奖
   - 失败原因在补偿成功后置空

3. **权限控制**：
   - 需要登录用户才能进行查询和补偿操作
   - 补偿操作会记录操作人信息

## 文件结构

```
src/main/java/wanda/member/mission/admin/api/
├── controller/
│   └── MissionExecutionRecordController.java     // 控制器
├── vo/
│   └── MissionExecutionRecordVo.java             // VO类
├── mapper/
│   └── MissionExecutionRecordMapper.java         // 映射器
└── test/
    └── MissionExecutionRecordControllerTest.java // 测试类
```

## 依赖说明

该接口依赖以下服务和DTO（需要在对应的contract模块中实现）：
- `MissionExecutionRecordAdminService`：任务执行记录管理服务
- `MissionExecutionRecordDto`：任务执行记录相关DTO类
- `CommonResponse`：通用响应类

## 测试用例

提供了完整的测试用例，包括：
- 多条件查询测试
- 全部状态查询测试
- 失败记录查询测试
- 补偿调用测试
- 时间范围查询测试
- 会员编号查询测试
