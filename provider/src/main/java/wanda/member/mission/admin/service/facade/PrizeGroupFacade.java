package wanda.member.mission.admin.service.facade;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import wanda.activity.prize.pool.admin.contract.PrizeGroupAdminService;
import wanda.activity.prize.pool.admin.contract.dto.PrizeGroupAdminDto;
import wanda.stark.core.codec.JsonUtils;

/**
 * <AUTHOR>
 **/
@Slf4j
@Component
@AllArgsConstructor
public class PrizeGroupFacade {
    private PrizeGroupAdminService prizeGroupAdminService;

    public boolean checkIsRelated(Integer prizeGroupId) {
        PrizeGroupAdminDto.GetPrizeGroupRequest request = new PrizeGroupAdminDto.GetPrizeGroupRequest();
        request.setId(prizeGroupId);

        log.info("校验奖池是否已关联入参:{}", JsonUtils.encode(request));
        PrizeGroupAdminDto.GetPrizeGroupResponse response = prizeGroupAdminService.getPrizeGroup(request);
        log.info("校验奖池是否已关联返回:{}", JsonUtils.encode(response));

        Integer activityId = response.getPrizeGroup().getActivityId();
        if (activityId != null) {
            log.info("奖池[{}]已关联活动[{}].", prizeGroupId, activityId);
            return true;
        } else {
            log.info("奖池[{}]未关联活动.", prizeGroupId);
            return false;
        }
    }

    public boolean relateActivityAndPrizeGroup(Integer prizeGroupId, Integer activityId, Integer originPrizeGroupId) {
        PrizeGroupAdminDto.RelationPrizeGroupRequest request = new PrizeGroupAdminDto.RelationPrizeGroupRequest();
        request.setId(prizeGroupId);
        request.setActivityId(activityId);
        request.setOriginalId(originPrizeGroupId);
        log.info("关联活动和奖池入参:{}", JsonUtils.encode(request));
        PrizeGroupAdminDto.RelationPrizeGroupResponse response = prizeGroupAdminService.relationPrizeGroup(request);
        log.info("关联活动和奖池返回:{}", JsonUtils.encode(response));
        return response.isSuccess();
    }


}
