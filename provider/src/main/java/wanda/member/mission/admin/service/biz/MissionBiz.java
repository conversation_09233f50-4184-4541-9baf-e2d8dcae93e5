package wanda.member.mission.admin.service.biz;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import wanda.commons.oplog.contract.AsyncOplogService;
import wanda.member.mission.admin.contract.dto.CommonResponse;
import wanda.member.mission.admin.contract.dto.MissionDto;
import wanda.member.mission.admin.contract.enums.ErrorCodeEnum;
import wanda.member.mission.admin.service.facade.ActivityFacade;
import wanda.member.mission.admin.service.facade.OpLogFacade;
import wanda.member.mission.admin.service.facade.PrizeGroupFacade;
import wanda.member.mission.admin.service.mapper.MissionMapper;
import wanda.member.mission.common.contract.enums.MissionStatusEnum;
import wanda.member.mission.common.service.dao.MissionDao;
import wanda.member.mission.common.service.po.MissionPo;
import wanda.stark.core.data.PageResult;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Slf4j
@Component
@AllArgsConstructor
public class MissionBiz {
    private OpLogFacade opLogFacade;
    private PrizeGroupFacade prizeGroupFacade;
    private ActivityFacade activityFacade;
    private MissionDao missionDao;

    public CommonResponse insert(MissionDto.SaveRequest request) {
        Integer prizeGroupId = request.getPrizeGroupId();
        MissionPo missionPo = MissionMapper.toInsertPo(request);

        //  校验奖池是否已被关联
        if (prizeGroupFacade.checkIsRelated(prizeGroupId)) {
            return new CommonResponse(ErrorCodeEnum.PRIZE_RELATED);
        }

        //  创建活动
        Integer activityId = activityFacade.create(missionPo, request.getOperatorId());
        //  关联活动和奖池
        boolean result = prizeGroupFacade.relateActivityAndPrizeGroup(prizeGroupId, activityId, null);
        if (!result) {
            return new CommonResponse(ErrorCodeEnum.PRIZE_RELATE_FAILED);
        }

        missionPo.setActivityId(activityId);
        missionDao.insertOne(missionPo);

        opLogFacade.asyncCreateOplog(missionPo);
        return new CommonResponse(ErrorCodeEnum.SUCCESS);
    }

    public CommonResponse update(MissionDto.SaveRequest request) {
        long missionId = request.getId();
        int newPrizeGroupId = request.getPrizeGroupId();
        MissionPo oldMission = missionDao.selectOne(missionId);
        int oldPrizeGroupId = oldMission.getPrizeGroupId();
        int activityId = oldMission.getActivityId();
        MissionPo missionPo = MissionMapper.toUpdatePo(request, oldMission);

        //  更新活动
        activityFacade.update(missionPo, activityId, request.getOperatorId());

        //  切换奖池
        if (newPrizeGroupId != oldPrizeGroupId) {
            //  校验新奖池是否已被关联
            if (prizeGroupFacade.checkIsRelated(newPrizeGroupId)) {
                return new CommonResponse(ErrorCodeEnum.PRIZE_RELATED);
            }
            //  重新关联活动和奖池
            boolean result = prizeGroupFacade.relateActivityAndPrizeGroup(newPrizeGroupId, activityId, oldPrizeGroupId);
            if (!result) {
                return new CommonResponse(ErrorCodeEnum.PRIZE_RELATE_FAILED);
            }
        }

        missionDao.saveOne(missionPo);
        return new CommonResponse(ErrorCodeEnum.SUCCESS);
    }


    public void updateStatus(MissionDto.UpdateStatusRequest request) {
        MissionStatusEnum status = MissionStatusEnum.valueOf(request.getStatus());
        missionDao.updateStatus(request.getId(), status, request.getOperatorId(), request.getOperatorName());
    }

    public MissionDto.MissionDetail get(Long missionId) {
        MissionPo missionPo = missionDao.selectOne(missionId);
        return MissionMapper.toDto(missionPo);
    }

    public MissionDto.PageListResponse pageList(MissionDto.PageListRequest request) {
        PageResult<MissionPo> pageResult = missionDao.pageList(MissionMapper.toQuery(request));

        MissionDto.PageListResponse response = new MissionDto.PageListResponse();
        int totalCount = pageResult.getTotalCount();
        response.setCount(totalCount);
        if (totalCount > 0) {
            response.setMissionDetailList(pageResult.getItems().stream().map(MissionMapper::toDto).collect(Collectors.toList()));
        }
        return response;
    }


}
