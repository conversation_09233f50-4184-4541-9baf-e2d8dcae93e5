package wanda.member.mission.admin.service.mapper;

import wanda.member.mission.admin.contract.dto.MissionDto;
import wanda.member.mission.common.contract.enums.MissionStatusEnum;
import wanda.member.mission.common.service.po.MissionPo;
import wanda.member.mission.common.service.po.MissionQuery;
import wanda.stark.mapper.Mapper;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 **/
public class MissionMapper {

    public static MissionPo toInsertPo(MissionDto.SaveRequest request) {
        MissionPo missionPo = new MissionPo();
        Mapper<MissionDto.SaveRequest, MissionPo> mapper = Mapper.getMapper(MissionDto.SaveRequest.class, MissionPo.class);
        mapper.copy(request, missionPo);

        missionPo.setStatus(MissionStatusEnum.ON_LINE.value());
        missionPo.setCreateUserId(request.getOperatorId());
        missionPo.setCreateUser(request.getOperatorName());
        missionPo.setCreateTime(LocalDateTime.now());
        missionPo.setUpdateUserId(request.getOperatorId());
        missionPo.setUpdateUser(request.getOperatorName());
        missionPo.setUpdateTime(LocalDateTime.now());
        return missionPo;
    }

    public static MissionPo toUpdatePo(MissionDto.SaveRequest request, MissionPo oldMission) {
        Mapper<MissionDto.SaveRequest, MissionPo> mapper = Mapper.getMapper(MissionDto.SaveRequest.class, MissionPo.class);
        mapper.copy(request, oldMission);

        oldMission.setUpdateTime(LocalDateTime.now());
        oldMission.setUpdateUserId(request.getOperatorId());
        oldMission.setUpdateUser(request.getOperatorName());
        return oldMission;
    }

    public static MissionQuery toQuery(MissionDto.PageListRequest request) {
        MissionQuery missionQuery = new MissionQuery();
        Mapper<MissionDto.PageListRequest, MissionQuery> mapper = Mapper.getMapper(MissionDto.PageListRequest.class, MissionQuery.class);
        mapper.copy(request, missionQuery);
        return missionQuery;
    }

    public static MissionDto.MissionDetail toDto(MissionPo missionPo) {
        MissionDto.MissionDetail detail = new MissionDto.MissionDetail();
        Mapper<MissionPo, MissionDto.MissionDetail> mapper = Mapper.getMapper(MissionPo.class, MissionDto.MissionDetail.class);
        mapper.copy(missionPo, detail);
        return detail;
    }


}
