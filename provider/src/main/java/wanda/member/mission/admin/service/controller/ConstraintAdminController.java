package wanda.member.mission.admin.service.controller;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import wanda.member.mission.admin.contract.ConstraintAdminService;
import wanda.member.mission.admin.contract.dto.CommonResponse;
import wanda.member.mission.admin.contract.dto.ConstraintDto;
import wanda.member.mission.admin.service.biz.ConstraintBiz;

/**
 * <AUTHOR>
 **/
@Slf4j
@RestController
@AllArgsConstructor
public class ConstraintAdminController implements ConstraintAdminService {
    private ConstraintBiz constraintBiz;

    @Override
    public CommonResponse save(ConstraintDto.SaveRequest request) {
        log.info("保存约束入参:{}", request);

        constraintBiz.save(request);

        CommonResponse response = new CommonResponse();
        log.info("保存约束返回:{}", response);
        return response;
    }

    @Override
    public ConstraintDto.ConstraintDetail get(Long constraintId) {
        log.info("查询约束入参:{}", constraintId);

        ConstraintDto.ConstraintDetail detail = constraintBiz.get(constraintId);
        log.info("查询约束返回:{}", detail);
        return detail;
    }

    @Override
    public ConstraintDto.PageListResponse pageList(ConstraintDto.PageListRequest request) {
        log.info("分页查询约束入参:{}", request);

        ConstraintDto.PageListResponse response = constraintBiz.pageList(request);
        log.info("分页查询约束返回:{}", response);
        return response;
    }
}
