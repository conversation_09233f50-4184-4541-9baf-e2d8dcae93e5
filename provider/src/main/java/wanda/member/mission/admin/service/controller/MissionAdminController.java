package wanda.member.mission.admin.service.controller;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import wanda.member.mission.admin.contract.MissionAdminService;
import wanda.member.mission.admin.contract.dto.CommonResponse;
import wanda.member.mission.admin.contract.dto.MissionDto;
import wanda.member.mission.admin.service.biz.MissionBiz;

/**
 * <AUTHOR>
 **/
@Slf4j
@RestController
@AllArgsConstructor
public class MissionAdminController implements MissionAdminService {
    private MissionBiz missionBiz;

    @Override
    public CommonResponse save(MissionDto.SaveRequest request) {
        log.info("保存任务入参:{}", request);

        Long missionId = request.getId();
        CommonResponse response;
        if (missionId == null || missionId == 0) {
            response = missionBiz.insert(request);
        } else {
            response = missionBiz.update(request);
        }
        log.info("保存任务返回:{}", response);
        return response;
    }

    @Override
    public CommonResponse updateStatus(MissionDto.UpdateStatusRequest request) {
        log.info("更新任务入参:{}}", request);

        missionBiz.updateStatus(request);

        CommonResponse response = new CommonResponse();
        log.info("更新任务返回:{}", response);
        return response;
    }

    @Override
    public MissionDto.MissionDetail get(Long missionId) {
        log.info("获取任务详情入参:[missionId:{}]", missionId);

        MissionDto.MissionDetail detail = missionBiz.get(missionId);
        log.info("获取任务详情返回:{}", detail);
        return detail;
    }

    @Override
    public MissionDto.PageListResponse pageList(MissionDto.PageListRequest request) {
        log.info("分页查询任务列表入参:{}", request);

        MissionDto.PageListResponse response = missionBiz.pageList(request);
        log.info("分页查询任务列表返回:{}", response);
        return response;
    }
}
