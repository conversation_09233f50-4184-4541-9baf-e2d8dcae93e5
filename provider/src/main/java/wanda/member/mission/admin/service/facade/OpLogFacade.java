package wanda.member.mission.admin.service.facade;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import wanda.commons.oplog.contract.AsyncOplogService;
import wanda.commons.oplog.contract.dto.OplogDto;
import wanda.member.mission.common.service.po.MissionPo;

/**
 * <AUTHOR>
 **/
@Slf4j
@Component
@AllArgsConstructor
public class OpLogFacade {
    private static final String OP_LOG_TYPE = "MISSION";
    private AsyncOplogService oplogService;

    public void asyncCreateOplog(MissionPo missionPo) {
        OplogDto.Oplog request = OplogDto.Oplog.builder()
                .objectId(missionPo.getId())
                .objectType(OP_LOG_TYPE)
                .title("创建任务")
                // 设置标签，方便根据标签进行查询
                .tag("创建任务")
                .content(missionPo)
                .build();
        oplogService.log(request);
    }

    public void asyncUpdateOplog(MissionPo oldMissionPo, MissionPo newMissionPo) {
        OplogDto.Oplog request = OplogDto.Oplog.builder()
                .objectId(newMissionPo.getId())
                .objectType(OP_LOG_TYPE)
                .title("修改任务")
                .tag("修改任务")
                .content(oldMissionPo, newMissionPo)
                .build();
        oplogService.log(request);
    }




}
