package wanda.member.mission.admin.service.biz;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import wanda.member.mission.admin.contract.dto.ConstraintDto;
import wanda.member.mission.admin.service.mapper.ConstraintMapper;
import wanda.member.mission.common.service.dao.ConstraintDao;
import wanda.member.mission.common.service.po.ConstraintPo;
import wanda.stark.core.data.PageResult;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Slf4j
@Component
@AllArgsConstructor
public class ConstraintBiz {
    private ConstraintDao constraintDao;

    public void save(ConstraintDto.SaveRequest request) {
        Long constraintId = request.getId();
        if (constraintId == null || constraintId == 0) {
            //  新增
            ConstraintPo constraintPo = ConstraintMapper.toInsertPo(request);
            constraintDao.insertOne(constraintPo);
        } else {
            //  更新
            ConstraintPo oldConstraint = constraintDao.selectOne(constraintId);
            ConstraintPo constraintPo = ConstraintMapper.toUpdatePo(request, oldConstraint);
            constraintDao.saveOne(constraintPo);
        }
    }

    public ConstraintDto.ConstraintDetail get(Long constraintId) {
        ConstraintPo constraintPo = constraintDao.selectOne(constraintId);
        return ConstraintMapper.toDto(constraintPo);
    }

    public ConstraintDto.PageListResponse pageList(ConstraintDto.PageListRequest request) {
        PageResult<ConstraintPo> pageResult = constraintDao.pageList(ConstraintMapper.toQuery(request));

        ConstraintDto.PageListResponse response = new ConstraintDto.PageListResponse();
        int totalCount = pageResult.getTotalCount();
        response.setCount(totalCount);
        if (totalCount > 0) {
            response.setConstraintDetailList(pageResult.getItems().stream().map(ConstraintMapper::toDto).collect(Collectors.toList()));
        }
        return response;
    }


}
