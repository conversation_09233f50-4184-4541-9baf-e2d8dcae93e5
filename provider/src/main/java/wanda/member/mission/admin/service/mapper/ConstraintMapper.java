package wanda.member.mission.admin.service.mapper;

import wanda.member.mission.admin.contract.dto.ConstraintDto;
import wanda.member.mission.common.service.po.ConstraintPo;
import wanda.member.mission.common.service.po.ConstraintQuery;
import wanda.stark.mapper.Mapper;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 **/
public class ConstraintMapper {


    public static ConstraintPo toInsertPo(ConstraintDto.SaveRequest request) {
        ConstraintPo constraintPo = new ConstraintPo();
        Mapper<ConstraintDto.SaveRequest, ConstraintPo> mapper = Mapper.getMapper(ConstraintDto.SaveRequest.class, ConstraintPo.class);
        mapper.copy(request, constraintPo);

        constraintPo.setCreateUserId(request.getOperatorId());
        constraintPo.setCreateUser(request.getOperatorName());
        constraintPo.setCreateTime(LocalDateTime.now());
        constraintPo.setUpdateUserId(request.getOperatorId());
        constraintPo.setUpdateUser(request.getOperatorName());
        constraintPo.setUpdateTime(LocalDateTime.now());
        return constraintPo;
    }

    public static ConstraintPo toUpdatePo(ConstraintDto.SaveRequest request, ConstraintPo oldConstraint) {
        Mapper<ConstraintDto.SaveRequest, ConstraintPo> mapper = Mapper.getMapper(ConstraintDto.SaveRequest.class, ConstraintPo.class);
        mapper.copy(request, oldConstraint);

        oldConstraint.setUpdateTime(LocalDateTime.now());
        oldConstraint.setUpdateUserId(request.getOperatorId());
        oldConstraint.setUpdateUser(request.getOperatorName());
        return oldConstraint;
    }

    public static ConstraintQuery toQuery(ConstraintDto.PageListRequest request) {
        ConstraintQuery constraintQuery = new ConstraintQuery();
        Mapper<ConstraintDto.PageListRequest, ConstraintQuery> mapper = Mapper.getMapper(ConstraintDto.PageListRequest.class, ConstraintQuery.class);
        mapper.copy(request, constraintQuery);
        return constraintQuery;
    }

    public static ConstraintDto.ConstraintDetail toDto(ConstraintPo constraintPo) {
        ConstraintDto.ConstraintDetail detail = new ConstraintDto.ConstraintDetail();
        Mapper<ConstraintPo, ConstraintDto.ConstraintDetail> mapper = Mapper.getMapper(ConstraintPo.class, ConstraintDto.ConstraintDetail.class);
        mapper.copy(constraintPo, detail);
        return detail;
    }

}
