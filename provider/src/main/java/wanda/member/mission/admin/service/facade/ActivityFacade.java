package wanda.member.mission.admin.service.facade;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import wanda.activity.basis.admin.contract.ActivityBasisInfoService;
import wanda.activity.basis.admin.contract.dto.ActivityBasisInfoDto;
import wanda.member.mission.common.service.po.MissionPo;
import wanda.stark.core.codec.JsonUtils;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 **/
@Slf4j
@Component
@AllArgsConstructor
public class ActivityFacade {
    private static final int MISSION_ACTIVITY_TYPE = 21;
    private static final LocalDateTime DEFAULT_END_TIME = LocalDateTime.of(3000, 1, 1, 0, 0, 0);
    private ActivityBasisInfoService activityBasisInfoService;


    public int create(MissionPo missionPo, int operatorId) {
        ActivityBasisInfoDto.CreateRequest request = new ActivityBasisInfoDto.CreateRequest();
        request.setActivityName(missionPo.getName());
        request.setCategory(MISSION_ACTIVITY_TYPE);
        request.setStartTime(missionPo.getOnlineTime() == null ? LocalDateTime.now() : missionPo.getOnlineTime());
        request.setEndTime(missionPo.getOfflineTime() == null ? DEFAULT_END_TIME : missionPo.getOfflineTime());
        request.setCreateUser(operatorId);
        log.info("创建活动入参:{}", JsonUtils.encode(request));
        ActivityBasisInfoDto.CreateResponse response = activityBasisInfoService.create(request);
        log.info("创建活动返回:{}", JsonUtils.encode(response));
        return response.getId();
    }


    public boolean update(MissionPo missionPo, int activityId, int operatorId) {
        ActivityBasisInfoDto.UpdateRequest request = new ActivityBasisInfoDto.UpdateRequest();
        request.setId(activityId);
        request.setActivityName(missionPo.getName());
        request.setStartTime(missionPo.getOnlineTime() == null ? LocalDateTime.now() : missionPo.getOnlineTime());
        request.setEndTime(missionPo.getOfflineTime() == null ? DEFAULT_END_TIME : missionPo.getOfflineTime());
        request.setCategory(MISSION_ACTIVITY_TYPE);
        request.setUpdateUser(operatorId);
        log.info("更新活动入参:{}", JsonUtils.encode(request));
        ActivityBasisInfoDto.UpdateResponse response = activityBasisInfoService.update(request);
        log.info("更新活动返回:{}", JsonUtils.encode(response));
        return response.isSuccess();
    }


}
