package wanda.member.mission.admin.service.test;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import wanda.member.mission.admin.contract.dto.CommonResponse;
import wanda.member.mission.admin.contract.dto.ConstraintDto;
import wanda.member.mission.admin.service.ServiceApplication;
import wanda.member.mission.admin.service.controller.ConstraintAdminController;
import wanda.member.mission.common.contract.enums.PerioUnitEnum;
import wanda.member.mission.common.contract.enums.PeriodEnum;
import wanda.stark.core.test.TestBase;

import java.util.ArrayList;

/**
 * <AUTHOR>
 **/
@SpringBootTest(classes = {ServiceApplication.class})
public class ConstraintAdminServiceTest extends TestBase {

    @Autowired
    private ConstraintAdminController service;

    @Test
    public void saveTest() {
        for (int flag = 0; flag < 14; flag++) {
//            int flag = 111111111;
            ConstraintDto.SaveRequest request = new ConstraintDto.SaveRequest();
//            request.setId(1L);
            request.setName("test_name_" + flag);
            request.setDesc("test_desc_" + flag);
            request.setPeriod(PeriodEnum.LIMITLESS.value());
            request.setPeriodValue(flag);
            request.setPeriodUnit(PerioUnitEnum.YEAR.value());
            request.setMaxTimes(flag);
            request.setSceneId(flag);

            request.setPageIdList(new ArrayList<>() {{
                add(1);
                add(2);
                add(3);
            }});
            request.setPageUrlList(new ArrayList<>() {{
                add("aaa_");
                add("bbb_");
                add("ccc_");
            }});
            request.setPageMaxTimes(flag);
            request.setDirection(flag);
            request.setFilmIdList(new ArrayList<>() {{
                add(1);
                add(2);
                add(3);
            }});
            request.setGoodsIdList(new ArrayList<>() {{
                add(1);
                add(2);
                add(3);
            }});
            request.setIsCheckBuyTicket(true);
            request.setBuyTicketDays(flag);

            request.setOperatorId(flag);
            request.setOperatorName("update_" + flag);

            CommonResponse response = service.save(request);
            System.out.println("response == " + response);
        }
    }

    @Test
    public void getTest() {
        Long constraintId = 1L;
        service.get(constraintId);
    }

    @Test
    public void pageListTest() {
        ConstraintDto.PageListRequest request = new ConstraintDto.PageListRequest();
//        request.setConstraintId(1L);
//        request.setName("2");
        request.setSceneId(13);

        request.setPageIndex(1);
        request.setPageSize(10);
        service.pageList(request);
    }

}
