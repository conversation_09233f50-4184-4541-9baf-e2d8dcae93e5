package wanda.member.mission.admin.service.test;

import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import wanda.member.mission.admin.contract.dto.CommonResponse;
import wanda.member.mission.admin.contract.dto.MissionDto;
import wanda.member.mission.admin.service.ServiceApplication;
import wanda.member.mission.admin.service.controller.MissionAdminController;
import wanda.member.mission.common.contract.enums.MissionStatusEnum;
import wanda.stark.core.test.TestBase;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 **/
@SpringBootTest(classes = {ServiceApplication.class})
public class MissionAdminServiceTest extends TestBase {

    @Autowired
    private MissionAdminController service;

    @Test
    public void saveTest() {
//        for (int flag = 1; flag < 14; flag++) {
        int flag = 698768;
        MissionDto.SaveRequest request = new MissionDto.SaveRequest();
//        request.setId(32L);
        request.setSceneId(flag);
        request.setType(flag);
        request.setSceneName("asdf");
        request.setConstraintId(flag);
        request.setPrizeGroupId(1354);
        request.setAutoReward(true);
        request.setName("想看发成长值任务");
        request.setDesc("test_desc_" + flag);
        request.setIcon("test_icon_" + flag);
        request.setLoginIcon("test_login_icon_" + flag);
        request.setPosition(flag);
        request.setOnlineTime("2025-04-26 13:23:45");
        request.setOfflineTime("2025-08-15 21:16:37");
        request.setUndoneIcon("test_undone_icon_" + flag);
        request.setReceiveIcon("test_receive_icon_" + flag);
        request.setDoneIcon("test-done-icon_" + flag);
        request.setOperatorId(flag);
        request.setOperatorName("op-name_" + flag);

        List<MissionDto.PublishPlatform> platformList = new ArrayList<>();
        for (int i = 5; i < 7; i++) {
            MissionDto.PublishPlatform platform = new MissionDto.PublishPlatform();
            platform.setPlatformType(i);
            platform.setLogicalOperator(i);
            platform.setVersion("ver_" + i);
            platform.setLink("line_" + i);
            platformList.add(platform);
        }
        request.setPlatformList(platformList);

        CommonResponse response = service.save(request);
        System.out.println("response == " + response);
//        }
    }

    @Test
    public void updateStatusTest() {
        MissionDto.UpdateStatusRequest request = new MissionDto.UpdateStatusRequest();
        request.setId(33L);
        request.setStatus(MissionStatusEnum.OFF_LINE.value());
        request.setOperatorId(23);
        request.setOperatorName("update_user");

        CommonResponse response = service.updateStatus(request);
        System.out.println("response == " + response);
    }

    @Test
    public void getTest() {
        Long missionId = 18L;
        service.get(missionId);
    }

    @Test
    public void pageListTest() {
        MissionDto.PageListRequest request = new MissionDto.PageListRequest();
//        request.setMissionId(4L);
        request.setName("2");
//        request.setSceneId(14);
//        request.setStatus(MissionStatusEnum.ON_LINE.value());
//        request.setConstraintId(13);
//        request.setPrizeGroupId(15);

//        request.setStartTime(LocalDateTime.of(2025,5,1,0,0,0));
//        request.setEndTime(LocalDateTime.of(2025,5,15,0,0,0));

        request.setPageIndex(1);
        request.setPageSize(10);
        service.pageList(request);
    }

}
